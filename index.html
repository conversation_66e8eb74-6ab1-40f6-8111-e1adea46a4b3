<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Space Phoenix – Backend Developer Portfolio</title>
  <meta name="description" content="Backend developer portfolio with space theme, phoenix SVG, <PERSON>is smooth scroll, GSAP, and Anime.js animations." />
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://unpkg.com" crossorigin>
  <!-- Simple favicon -->
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔥</text></svg>"">
  <style>
    :root {
      --bg:#07070a;           /* deep space */
      --panel:#0c0f16;        /* panels */
      --text:#eaeaf2;         /* base text */
      --muted:#9aa0a6;        /* muted text */
      --flame:#ff6b35;        /* phoenix flame */
      --glow:#ffd56b;         /* soft glow */
      --accent:#6c63ff;       /* cosmic purple */
      --grid: 1200px;
    }

    /* Performance optimizations */
    * {
      box-sizing: border-box;
    }

    /* Reduce motion for accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      #starfield, .space-dust, #phoenixParticleCanvas {
        display: none !important;
      }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      :root {
        --bg: #000000;
        --panel: #1a1a1a;
        --text: #ffffff;
        --muted: #cccccc;
        --flame: #ff8800;
        --glow: #ffff00;
        --accent: #8888ff;
      }
    }

    /* Loading states */
    .loading {
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.6s ease;
    }

    .loaded {
      opacity: 1;
      transform: translateY(0);
    }

    /* Focus styles for accessibility */
    .btn:focus, input:focus, textarea:focus, [tabindex]:focus {
      outline: 2px solid var(--glow);
      outline-offset: 2px;
    }

    /* Skip link for screen readers */
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--panel);
      color: var(--text);
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 1000;
    }

    .skip-link:focus {
      top: 6px;
    }

    * { box-sizing: border-box; }
    html, body {
      height: 100%;
      background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
                  radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
                  var(--bg);
      color: var(--text);
      margin: 0;
      font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
      overflow-x: hidden;
    }
    a { color: var(--glow); text-decoration: none; }
    a:hover { text-decoration: underline; }

    /* Global layout */
    header {
      position: fixed; inset-inline: 0; top: 0; z-index: 50;
      backdrop-filter: saturate(120%) blur(10px);
      background: linear-gradient(to bottom, rgba(7,7,10,.75), transparent);
      border-bottom: 1px solid rgba(255,255,255,.06);
    }
    .nav {
      max-width: var(--grid); margin: 0 auto; padding: .6rem 1rem; display:flex; align-items:center; justify-content:space-between;
    }
    .brand { display:flex; gap:.6rem; align-items:center; font-weight:700; letter-spacing:.3px; }
    .dot { width: 10px; height: 10px; border-radius: 9999px; background: linear-gradient(180deg, var(--glow), var(--flame)); box-shadow: 0 0 12px var(--glow); }
    .menu { display:flex; gap:1rem; font-size:.95rem; }
    .menu a { opacity:.9 }

    main { position: relative; }

    /* Starfield canvas */
    #starfield { position: fixed; inset:0; z-index:-2; display:block; }
    /* Subtle dust overlay for depth */
    .space-dust {
      position: fixed; inset:0; pointer-events:none; z-index:-1;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><filter id="f"><feTurbulence baseFrequency="0.9" numOctaves="2" type="fractalNoise"/></filter><rect width="200" height="200" opacity="0.03" filter="url(%23f)"/></svg>') repeat;
      mix-blend-mode: screen;
    }

    /* Sections */
    section { padding: 7rem 1rem; }
    .wrap { max-width: var(--grid); margin: 0 auto; }

    /* HERO */
    .hero { display:grid; min-height: 100dvh; place-items:center; position: relative; }
    .hero-inner { display:grid; grid-template-columns: 1.2fr 1fr; gap: 3rem; align-items:center; width: 100%; }
    .hero p.kicker { color: var(--muted); text-transform: uppercase; font-size: .8rem; letter-spacing: .2em; margin: 0 0 .6rem; }
    .title { font-size: clamp(2.2rem, 5vw, 4rem); line-height: 1.05; margin: 0 0 1rem; font-weight: 800; }
    .subtitle { color: var(--muted); font-size: clamp(1rem, 2vw, 1.2rem); max-width: 60ch; }

    .cta { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
    .btn { border:1px solid rgba(255,255,255,.12); background: rgba(255,255,255,.03); color: var(--text); padding:.8rem 1rem; border-radius: 16px; font-weight:600; cursor:pointer; }
    .btn.primary { background: linear-gradient(180deg, rgba(255,213,107,.22), rgba(255,107,53,.22)); border-color: rgba(255,213,107,.45); box-shadow: 0 6px 30px rgba(255,213,107,.18); }
    .btn:hover { transform: translateY(-2px); }

    .phoenix-wrap { position: relative; }
    .phoenix-glow { position:absolute; inset: -10%; filter: blur(40px); opacity:.35; background: radial-gradient(closest-side, var(--glow), transparent 65%); border-radius: 50%; }
    svg#phoenix { width: min(480px, 38vw); display:block; transform-origin: 50% 50%; }

    /* ABOUT */
    .panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.00)); border:1px solid rgba(255,255,255,.06); border-radius: 20px; padding: 1.2rem; }
    .about { display:grid; gap: 1.2rem; grid-template-columns: 1.2fr .8fr; align-items: stretch; }
    .terminal { background: #0a0d13; border-radius: 14px; border:1px solid rgba(255,255,255,.06); position: relative; overflow:hidden; }
    .terminal .bar { display:flex; gap:.5rem; align-items:center; padding:.6rem .8rem; border-bottom:1px solid rgba(255,255,255,.06); background: linear-gradient(180deg, rgba(255,255,255,.04), transparent); }
    .status-dot { width:10px; height:10px; border-radius:9999px; background:#ff5f56; box-shadow: 0 0 8px rgba(255,95,86,.8); }
    .status-dot.yellow { background:#ffbd2e; box-shadow:0 0 8px rgba(255,189,46,.8); }
    .status-dot.green { background:#27c93f; box-shadow:0 0 8px rgba(39,201,63,.8); }
    .terminal pre { margin: 0; padding: 1rem; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; color: #c8e1ff; font-size: .95rem; min-height: 220px; white-space: pre-wrap; }

    /* ENHANCED PROJECTS */
    .projects-grid { display:grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; perspective: 1000px; }
    .card {
      position:relative; overflow:hidden; border-radius: 20px; background: var(--panel);
      border:1px solid rgba(255,255,255,.08); padding:1.5rem; min-height: 280px;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      transform-style: preserve-3d;
      cursor: pointer;
    }
    .card:hover {
      transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
      box-shadow: 0 20px 60px rgba(0,0,0,.4), 0 0 0 1px rgba(255,213,107,.2);
      border-color: rgba(255,213,107,.3);
    }
    .card h3 { margin:.4rem 0 .6rem; font-size: 1.3rem; }
    .card p { color: var(--muted); line-height: 1.6; }
    .card .shine {
      position:absolute; inset:-30%;
      background: conic-gradient(from 0deg, rgba(255,213,107,.12), transparent 25%, transparent 75%, rgba(255,213,107,.12));
      transform: rotate(0deg); opacity:.6; pointer-events:none;
      transition: opacity 0.3s ease;
    }
    .card:hover .shine { opacity: 1; }

    .card .tech-stack {
      display: flex; flex-wrap: wrap; gap: 0.4rem; margin-top: 1rem;
    }
    .card .tech-badge {
      background: rgba(255,255,255,.06); border: 1px solid rgba(255,255,255,.1);
      padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
      color: var(--glow); transition: all 0.3s ease;
    }
    .card:hover .tech-badge {
      background: rgba(255,213,107,.1); border-color: rgba(255,213,107,.3);
      transform: translateY(-2px);
    }

    .card .project-icon {
      position: absolute; top: 1rem; right: 1rem;
      width: 40px; height: 40px; border-radius: 50%;
      background: linear-gradient(135deg, var(--glow), var(--flame));
      display: flex; align-items: center; justify-content: center;
      font-size: 1.2rem; opacity: 0.8;
      transition: all 0.3s ease;
    }
    .card:hover .project-icon {
      transform: rotate(360deg) scale(1.1);
      opacity: 1;
    }

    /* SKILLS */
    .skills { display:grid; grid-template-columns: .9fr 1.1fr; gap: 2rem; align-items:center; }
    .skill-cloud { position: relative; aspect-ratio: 1/1; }
    .skill-cloud svg { width: 100%; height: auto; display:block; }
    .badge { display:inline-flex; gap:.5rem; align-items:center; background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.08); padding:.5rem .7rem; border-radius: 9999px; margin: .25rem; font-size: .95rem; }

    /* CONTACT */
    form { display:grid; gap: .9rem; }
    label { font-size:.9rem; color: var(--muted); }
    input, textarea { width:100%; background: rgba(255,255,255,.03); border:1px solid rgba(255,255,255,.1); color: var(--text); border-radius: 12px; padding:.8rem; outline: none; }
    input:focus, textarea:focus { border-color: var(--glow); box-shadow: 0 0 0 3px rgba(255,213,107,.15); }
    textarea { min-height: 140px; }

    footer { padding: 2rem 1rem 3rem; color: var(--muted); text-align:center; }

    /* Utils */
    .muted { color: var(--muted); }
    .grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
      :root { --grid: 95%; }
      .projects-grid { grid-template-columns: repeat(2, 1fr); gap: 1.2rem; }
    }

    @media (max-width: 1000px) {
      .hero-inner { grid-template-columns: 1fr; text-align:center; gap: 2rem; }
      .phoenix-wrap { order:-1; }
      .about { grid-template-columns: 1fr; }
      .skills { grid-template-columns: 1fr; text-align: center; }
      .skill-cloud { max-width: 300px; margin: 0 auto; }

      /* Reduce motion on tablets */
      .card:hover { transform: translateY(-4px); }
    }

    @media (max-width: 768px) {
      section { padding: 4rem 1rem; }
      .title { font-size: clamp(1.8rem, 6vw, 2.5rem); }
      .projects-grid { grid-template-columns: 1fr; gap: 1rem; }
      .card { padding: 1.2rem; min-height: 240px; }
      .cta { flex-direction: column; align-items: center; }
      .btn { width: 100%; max-width: 280px; text-align: center; }

      /* Mobile navigation improvements */
      .nav { padding: 0.8rem 1rem; }
      .menu { gap: 0.8rem; font-size: 0.9rem; }

      /* Touch-friendly sizing */
      .card .tech-badge { padding: 0.4rem 0.8rem; }
      input, textarea { padding: 1rem; font-size: 16px; } /* Prevent zoom on iOS */
    }

    @media (max-width: 480px) {
      .hero { min-height: 90vh; }
      .title { font-size: 2rem; line-height: 1.1; }
      .subtitle { font-size: 1rem; }
      section { padding: 3rem 0.8rem; }
      .card { padding: 1rem; min-height: 200px; }
      .nav { flex-direction: column; gap: 1rem; text-align: center; }
      .menu { justify-content: center; flex-wrap: wrap; }

      /* Simplified animations for mobile */
      .card:hover { transform: none; box-shadow: 0 8px 25px rgba(0,0,0,.3); }
      .phoenix-wrap { max-width: 280px; margin: 0 auto; }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
      .card:hover { transform: none; }
      .card:active { transform: scale(0.98); }
      .btn:hover { transform: none; }
      .btn:active { transform: scale(0.95); }

      /* Disable complex animations on touch devices */
      .card .shine { display: none; }
      #phoenixParticleCanvas { display: none; }
    }

    /* High DPI displays */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      .dot { box-shadow: 0 0 8px var(--glow); }
      .status-dot { box-shadow: 0 0 6px rgba(255,95,86,.6); }
    }

    /* Landscape mobile orientation */
    @media (max-height: 500px) and (orientation: landscape) {
      .hero { min-height: 100vh; padding: 2rem 1rem; }
      .hero-inner { gap: 1.5rem; }
      section { padding: 3rem 1rem; }
    }

    /* Screen reader only class */
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
  </style>
</head>
<body>
  <!-- Skip link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <!-- Loading indicator -->
  <div id="loading-indicator" style="position: fixed; inset: 0; background: var(--bg); z-index: 1000; display: flex; align-items: center; justify-content: center; flex-direction: column; cursor: pointer;" onclick="this.style.display='none'; document.body.style.overflow='';">
    <div style="width: 60px; height: 60px; border: 3px solid rgba(255,213,107,0.3); border-top: 3px solid var(--glow); border-radius: 50%; animation: spin 1s linear infinite;"></div>
    <p style="margin-top: 1rem; color: var(--muted);">Loading Space Phoenix...</p>
    <p style="margin-top: 0.5rem; color: var(--muted); font-size: 0.9rem; opacity: 0.7;">Click anywhere to continue</p>
  </div>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>

  <canvas id="starfield" aria-hidden="true"></canvas>
  <div class="space-dust" aria-hidden="true"></div>

  <header role="banner">
    <nav class="nav" role="navigation" aria-label="Main navigation">
      <div class="brand">
        <span class="dot" aria-hidden="true"></span>
        <span>Space Phoenix</span>
      </div>
      <div class="menu">
        <a href="#hero" aria-label="Go to home section">Home</a>
        <a href="#about" aria-label="Go to about section">About</a>
        <a href="#projects" aria-label="Go to projects section">Projects</a>
        <a href="#skills" aria-label="Go to skills section">Tech</a>
        <a href="#contact" aria-label="Go to contact section">Contact</a>
      </div>
    </nav>
  </header>

  <main id="main-content" role="main">
    <!-- HERO -->
    <section id="hero" class="hero loading" aria-labelledby="hero-title">
      <div class="wrap hero-inner">
        <div>
          <p class="kicker">Backend Developer</p>
          <h1 id="hero-title" class="title">Rising from Code. Building the Future.</h1>
          <p class="subtitle">I design resilient APIs, distributed systems, and developer tooling. Obsessed with performance, observability, and elegant architectures.</p>
          <div class="cta">
            <a class="btn primary" href="#projects" aria-describedby="projects-desc">View Projects</a>
            <span id="projects-desc" class="sr-only">Navigate to projects showcase section</span>
            <a class="btn" href="#contact" aria-describedby="contact-desc">Get in Touch</a>
            <span id="contact-desc" class="sr-only">Navigate to contact form section</span>
          </div>
        </div>
        <div class="phoenix-wrap" data-parallax data-speed="0.05">
          <div class="phoenix-glow" aria-hidden="true"></div>
          <!-- Enhanced Phoenix SVG with particle effects -->
          <svg id="phoenix" viewBox="0 0 512 512" fill="none" stroke="url(#grad)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
            <defs>
              <linearGradient id="grad" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stop-color="var(--glow)"/>
                <stop offset="50%" stop-color="var(--flame)"/>
                <stop offset="100%" stop-color="var(--accent)"/>
              </linearGradient>
              <linearGradient id="fireGrad" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stop-color="var(--flame)" stop-opacity="0.8"/>
                <stop offset="50%" stop-color="var(--glow)" stop-opacity="0.6"/>
                <stop offset="100%" stop-color="var(--accent)" stop-opacity="0.3"/>
              </linearGradient>
              <filter id="phoenixGlow">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
              <radialGradient id="particleGrad" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stop-color="var(--glow)" stop-opacity="1"/>
                <stop offset="70%" stop-color="var(--flame)" stop-opacity="0.6"/>
                <stop offset="100%" stop-color="transparent" stop-opacity="0"/>
              </radialGradient>
            </defs>

            <!-- Particle system for phoenix fire -->
            <g id="phoenixParticles" opacity="0.7">
              <!-- Particles will be dynamically added here -->
            </g>

            <!-- Main phoenix body with enhanced paths -->
            <g filter="url(#phoenixGlow)">
              <path id="phoenixBody" d="M256 60c-38 62 6 116-20 168-20 38-72 42-116 26 62 26 112 6 144-36 32 42 82 62 144 36-44 16-96 12-116-26-26-52 18-106-20-168Z" fill="none" stroke="url(#grad)" stroke-width="2.5"/>
              <path id="phoenixWings" d="M116 248c38 20 72 38 92 70-18 16-44 20-68 14 28 10 52 26 62 52-18 2-36-2-50-10 34 30 66 60 104 78 38-18 70-48 104-78-14 8-32 12-50 10 10-26 34-42 62-52-24 6-50 2-68-14 20-32 54-50 92-70" fill="none" stroke="url(#grad)" stroke-width="2"/>
              <path id="phoenixTail" d="M248 126c-6 22-8 44-8 66m16-66c6 22 8 44 8 66" stroke="url(#fireGrad)" stroke-width="3" opacity="0.8"/>

              <!-- Additional flame details -->
              <path id="flameDetail1" d="M200 180c8-12 16-8 24 0 8 8 16 4 24-8" stroke="var(--flame)" stroke-width="1.5" opacity="0.6"/>
              <path id="flameDetail2" d="M264 180c8-12 16-8 24 0 8 8 16 4 24-8" stroke="var(--flame)" stroke-width="1.5" opacity="0.6"/>
            </g>

            <!-- Phoenix eye with glow -->
            <circle id="phoenixEye" cx="256" cy="140" r="3" fill="var(--glow)" opacity="0.9">
              <animate attributeName="opacity" values="0.9;1;0.9" dur="2s" repeatCount="indefinite"/>
            </circle>
          </svg>

          <!-- Particle canvas for advanced effects -->
          <canvas id="phoenixParticleCanvas" style="position: absolute; inset: 0; pointer-events: none; z-index: 1;"></canvas>
        </div>
      </div>
    </section>

    <!-- ABOUT -->
    <section id="about">
      <div class="wrap about">
        <div class="panel">
          <h2>About Me</h2>
          <p class="muted">I build back-ends that don’t blink under load. Experience with Node.js, Express, NestJS, Python (FastAPI), PostgreSQL, Redis, Kafka, Docker, and Kubernetes. I care about clean boundaries, excellent DX, and making systems observable.</p>
          <div class="grid-2" style="margin-top: .6rem;">
            <div class="badge">Node.js</div>
            <div class="badge">Express</div>
            <div class="badge">NestJS</div>
            <div class="badge">TypeScript</div>
            <div class="badge">Python</div>
            <div class="badge">FastAPI</div>
            <div class="badge">PostgreSQL</div>
            <div class="badge">Redis</div>
            <div class="badge">Kafka</div>
            <div class="badge">Docker</div>
            <div class="badge">Kubernetes</div>
            <div class="badge">AWS</div>
          </div>
        </div>
        <div class="terminal">
          <div class="bar">
            <span class="status-dot"></span>
            <span class="status-dot yellow"></span>
            <span class="status-dot green"></span>
            <span style="margin-left:.5rem; opacity:.8;">~/phoenix/run.sh</span>
          </div>
          <pre id="terminalText"></pre>
        </div>
      </div>
    </section>

    <!-- PROJECTS -->
    <section id="projects">
      <div class="wrap">
        <h2>Projects</h2>
        <p class="muted">Selected work with a focus on APIs, scalability, and reliability.</p>
        <div class="projects-grid" style="margin-top:1.5rem;">
          <article class="card" data-project="stellar">
            <div class="shine" aria-hidden="true"></div>
            <div class="project-icon">🚀</div>
            <h3>Stellar API Gateway</h3>
            <p>High-performance API gateway with advanced rate limiting, JWT authentication, and comprehensive telemetry. Handles 120k requests per second on Kubernetes with sub-millisecond latency.</p>
            <div class="tech-stack">
              <span class="tech-badge">Node.js</span>
              <span class="tech-badge">Express</span>
              <span class="tech-badge">Redis</span>
              <span class="tech-badge">Kubernetes</span>
              <span class="tech-badge">Prometheus</span>
            </div>
            <a class="btn" style="margin-top:1rem;" data-open>View Details</a>
          </article>

          <article class="card" data-project="nova">
            <div class="shine" aria-hidden="true"></div>
            <div class="project-icon">⚡</div>
            <h3>Event Nova</h3>
            <p>Distributed event streaming platform built on Kafka with transactional outbox pattern and idempotency guarantees. Designed for 99.999% uptime with automatic failover.</p>
            <div class="tech-stack">
              <span class="tech-badge">Kafka</span>
              <span class="tech-badge">PostgreSQL</span>
              <span class="tech-badge">Docker</span>
              <span class="tech-badge">TypeScript</span>
              <span class="tech-badge">Zookeeper</span>
            </div>
            <a class="btn" style="margin-top:1rem;" data-open>View Details</a>
          </article>

          <article class="card" data-project="constellation">
            <div class="shine" aria-hidden="true"></div>
            <div class="project-icon">🔭</div>
            <h3>Constellation Observability</h3>
            <p>Comprehensive observability platform with OpenTelemetry integration, distributed tracing, metrics collection, and intelligent alerting with SLO-based dashboards.</p>
            <div class="tech-stack">
              <span class="tech-badge">OpenTelemetry</span>
              <span class="tech-badge">Grafana</span>
              <span class="tech-badge">Jaeger</span>
              <span class="tech-badge">Prometheus</span>
              <span class="tech-badge">Python</span>
            </div>
            <a class="btn" style="margin-top:1rem;" data-open>View Details</a>
          </article>
        </div>
      </div>
    </section>

    <!-- SKILLS -->
    <section id="skills">
      <div class="wrap skills">
        <div>
          <h2>Tech Stack</h2>
          <p class="muted">A toolkit tuned for building reliable services and great developer experiences.</p>
          <div style="margin-top:.6rem; display:flex; flex-wrap:wrap;">
            <div class="badge">Node.js</div>
            <div class="badge">TypeScript</div>
            <div class="badge">Python</div>
            <div class="badge">FastAPI</div>
            <div class="badge">PostgreSQL</div>
            <div class="badge">Prisma/TypeORM</div>
            <div class="badge">Redis</div>
            <div class="badge">Kafka</div>
            <div class="badge">Docker</div>
            <div class="badge">Kubernetes</div>
            <div class="badge">AWS</div>
            <div class="badge">OpenTelemetry</div>
          </div>
        </div>
        <div class="skill-cloud" aria-hidden="true">
          <!-- Circular orbit labels, animated with Anime.js -->
          <svg viewBox="0 0 400 400">
            <defs>
              <filter id="softGlow">
                <feGaussianBlur stdDeviation="3" result="blur"/>
                <feMerge>
                  <feMergeNode in="blur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <g id="orbits" fill="none" stroke="rgba(255,255,255,.12)">
              <circle cx="200" cy="200" r="60"/>
              <circle cx="200" cy="200" r="110"/>
              <circle cx="200" cy="200" r="160"/>
            </g>
            <g id="labels" font-size="12" text-anchor="middle" fill="currentColor" filter="url(#softGlow)">
              <text x="200" y="35">Node</text>
              <text x="330" y="90">Postgres</text>
              <text x="365" y="210">Redis</text>
              <text x="320" y="320">Kafka</text>
              <text x="200" y="365">Docker</text>
              <text x="80"  y="320">Kubernetes</text>
              <text x="35"  y="210">AWS</text>
              <text x="70"  y="90">OpenTelemetry</text>
            </g>
            <circle id="core" cx="200" cy="200" r="8" fill="url(#grad)"/>
          </svg>
        </div>
      </div>
    </section>

    <!-- CONTACT -->
    <section id="contact">
      <div class="wrap">
        <h2>Contact</h2>
        <p class="muted">Have a challenge that needs firepower? I’m open to collaborations and roles.</p>
        <form>
          <div class="grid-2">
            <div>
              <label for="name">Name</label>
              <input id="name" placeholder="Ada Lovelace" />
            </div>
            <div>
              <label for="email">Email</label>
              <input id="email" type="email" placeholder="<EMAIL>" />
            </div>
          </div>
          <div>
            <label for="msg">Message</label>
            <textarea id="msg" placeholder="Tell me about your project..."></textarea>
          </div>
          <button class="btn primary" type="button">Send</button>
        </form>
      </div>
    </section>

    <footer>
      <div class="wrap">
        <div class="muted">© <span id="year"></span> Space Phoenix — Built with HTML, CSS, and JavaScript. Lenis × GSAP × Anime.js</div>
      </div>
    </footer>
  </main>

  <!-- Vendor libs with fallbacks -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
  <!-- Using custom Lenis fallback for better reliability -->

  <script>
    // Fallback smooth scroll implementation
    class FallbackLenis {
      constructor(options = {}) {
        this.lerp = options.lerp || 0.1;
        this.wheelMultiplier = options.wheelMultiplier || 1.2;
        this.callbacks = [];
        this.targetScroll = window.pageYOffset;
        this.currentScroll = window.pageYOffset;
        this.init();
      }

      init() {
        this.bindEvents();
        this.raf();
      }

      bindEvents() {
        window.addEventListener('wheel', (e) => {
          e.preventDefault();
          this.targetScroll += e.deltaY * this.wheelMultiplier;
          this.targetScroll = Math.max(0, Math.min(this.targetScroll, document.body.scrollHeight - window.innerHeight));
        }, { passive: false });
      }

      raf() {
        this.currentScroll += (this.targetScroll - this.currentScroll) * this.lerp;
        window.scrollTo(0, this.currentScroll);

        this.callbacks.forEach(callback => {
          callback({ scroll: this.currentScroll });
        });

        requestAnimationFrame(() => this.raf());
      }

      on(event, callback) {
        if (event === 'scroll') {
          this.callbacks.push(callback);
        }
      }

      scrollTo(target, options = {}) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (element) {
          const offset = options.offset || 0;
          this.targetScroll = element.offsetTop + offset;
        }
      }
    }

    // Initialize libraries with error handling
    let lenis, gsap, ScrollTrigger, anime;
    window.portfolioInitialized = false;

    // Check if libraries loaded successfully
    const initializeLibraries = () => {
      if (window.portfolioInitialized) return;

      try {
        let librariesLoaded = true;

        // Initialize GSAP
        if (typeof window.gsap !== 'undefined') {
          gsap = window.gsap;
          if (typeof window.ScrollTrigger !== 'undefined') {
            ScrollTrigger = window.ScrollTrigger;
            gsap.registerPlugin(ScrollTrigger);
          }
          console.log('✅ GSAP loaded successfully');
        } else {
          librariesLoaded = false;
        }

        // Initialize Anime.js
        if (typeof window.anime !== 'undefined') {
          anime = window.anime;
          console.log('✅ Anime.js loaded successfully');
        } else {
          librariesLoaded = false;
        }

        // Initialize smooth scrolling (using reliable fallback)
        console.log('✅ Using custom smooth scroll implementation');
        lenis = new FallbackLenis({ lerp: 0.1, wheelMultiplier: 1.2 });

        if (librariesLoaded) {
          console.log('✅ All core libraries loaded, starting full experience');
        } else {
          console.log('✅ Using enhanced fallback mode for maximum compatibility');
        }

        // Start the application
        window.portfolioInitialized = true;
        initializeApp();

      } catch (error) {
        console.error('Library initialization error:', error);
        // Fallback initialization
        if (!window.portfolioInitialized) {
          initializeFallback();
        }
      }
    };

    // Fallback initialization without external libraries
    const initializeFallback = () => {
      if (window.portfolioInitialized) return;

      console.log('🔄 Running in fallback mode');
      window.portfolioInitialized = true;

      // Basic smooth scroll fallback
      lenis = new FallbackLenis();

      // Basic animation fallbacks
      gsap = {
        to: (target, options) => {
          const elements = typeof target === 'string' ? document.querySelectorAll(target) : [target];
          elements.forEach(el => {
            if (el && el.style) {
              Object.keys(options).forEach(prop => {
                if (prop !== 'duration' && prop !== 'ease' && prop !== 'delay' && prop !== 'onComplete') {
                  if (prop === 'y') {
                    el.style.transform = `translateY(${options[prop]}px)`;
                  } else if (prop === 'opacity') {
                    el.style.opacity = options[prop];
                  } else if (prop === 'scale') {
                    el.style.transform = `scale(${options[prop]})`;
                  }
                }
              });
            }
          });
          // Call onComplete if provided
          if (options.onComplete) {
            setTimeout(options.onComplete, options.duration || 0);
          }
        },
        from: (target, options) => {
          // For 'from' animations, we'll just apply the end state immediately
          gsap.to(target, { ...options, duration: 0 });
        },
        timeline: () => ({
          to: gsap.to,
          from: gsap.from,
          call: (fn) => fn()
        }),
        utils: {
          toArray: (selector) => Array.from(document.querySelectorAll(selector))
        },
        registerPlugin: () => {} // No-op for fallback
      };

      anime = (options) => {
        const elements = document.querySelectorAll(options.targets);
        elements.forEach(el => {
          Object.keys(options).forEach(prop => {
            if (prop !== 'targets' && prop !== 'duration' && prop !== 'easing') {
              if (el.style) el.style[prop] = Array.isArray(options[prop]) ? options[prop][1] : options[prop];
            }
          });
        });
      };

      initializeApp();
    };

    // Wait for DOM and try to initialize
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initializeLibraries, 500);
      });
    } else {
      setTimeout(initializeLibraries, 500);
    }

    // Fallback timer - always ensure initialization
    setTimeout(() => {
      if (!window.portfolioInitialized) {
        console.log('🔄 Initializing with fallback for maximum compatibility');
        initializeFallback();
      }
    }, 2000);

    // Main application initialization
    const initializeApp = () => {
      console.log('🚀 Initializing Space Phoenix Portfolio');

      // Register GSAP plugins if available
      if (gsap && ScrollTrigger) {
        gsap.registerPlugin(ScrollTrigger);
      }

    // =========================
    // PERFORMANCE & ACCESSIBILITY SETUP
    // =========================

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // Performance monitoring
    const performanceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          console.log(`${entry.name}: ${entry.duration}ms`);
        }
      }
    });
    performanceObserver.observe({ entryTypes: ['measure'] });

    // Loading state management
    class LoadingManager {
      constructor() {
        this.loadingIndicator = document.getElementById('loading-indicator');
        this.loadedSections = new Set();
        this.totalSections = document.querySelectorAll('section').length;
        this.init();
      }

      init() {
        // Hide loading indicator after initial load
        window.addEventListener('load', () => {
          setTimeout(() => this.hideLoading(), 500);
        });

        // Force hide loading after maximum wait time
        setTimeout(() => {
          if (this.loadingIndicator && this.loadingIndicator.style.display !== 'none') {
            console.log('🔄 Force hiding loading screen after timeout');
            this.hideLoading();
          }
        }, 3000);

        // Progressive loading of sections
        this.setupIntersectionObserver();
      }

      hideLoading() {
        if (this.loadingIndicator) {
          gsap.to(this.loadingIndicator, {
            opacity: 0,
            duration: 0.5,
            onComplete: () => {
              this.loadingIndicator.style.display = 'none';
              this.revealContent();
            }
          });
        }
      }

      revealContent() {
        // Reveal sections progressively
        const sections = document.querySelectorAll('.loading');
        sections.forEach((section, index) => {
          gsap.to(section, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            delay: index * 0.1,
            ease: 'power3.out',
            onComplete: () => {
              section.classList.remove('loading');
              section.classList.add('loaded');
            }
          });
        });
      }

      setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !this.loadedSections.has(entry.target)) {
              this.loadedSections.add(entry.target);
              entry.target.classList.add('loaded');

              // Trigger section-specific animations
              this.animateSection(entry.target);
            }
          });
        }, { threshold: 0.1 });

        document.querySelectorAll('section').forEach(section => {
          observer.observe(section);
        });
      }

      animateSection(section) {
        if (prefersReducedMotion) return;

        const elements = section.querySelectorAll('h2, h3, p, .card, .badge, .btn');
        if (gsap && gsap.from) {
          gsap.from(elements, {
            y: 20,
            opacity: 0,
            duration: 0.6,
            stagger: 0.05,
            ease: 'power3.out'
          });
        } else {
          // Fallback: just make elements visible
          elements.forEach(el => {
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
          });
        }
      }
    }

    const loadingManager = new LoadingManager();

    // Error handling for failed animations
    window.addEventListener('error', (e) => {
      console.warn('Animation error caught:', e.error);
      // Fallback to basic functionality
      document.querySelectorAll('.loading').forEach(el => {
        el.classList.remove('loading');
        el.classList.add('loaded');
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      });
    });

    // Ensure all elements are visible after a timeout
    setTimeout(() => {
      document.querySelectorAll('.loading').forEach(el => {
        el.classList.remove('loading');
        el.classList.add('loaded');
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      });
    }, 3000);

    // Emergency fallback - force hide loading screen
    setTimeout(() => {
      const loadingIndicator = document.getElementById('loading-indicator');
      if (loadingIndicator) {
        console.log('🚨 Emergency: Force hiding loading screen');
        loadingIndicator.style.display = 'none';
        document.body.style.overflow = '';

        // Make sure all content is visible
        document.querySelectorAll('.loading').forEach(el => {
          el.classList.remove('loading');
          el.classList.add('loaded');
          el.style.opacity = '1';
          el.style.transform = 'translateY(0)';
        });
      }
    }, 5000);

    // =========================
    // 1) ENHANCED STARFIELD & SPACE EFFECTS
    // =========================
    const canvas = document.getElementById('starfield');
    const ctx = canvas.getContext('2d');
    let stars = [], nebulae = [], shootingStars = [], W, H, pixelRatio = Math.min(2, window.devicePixelRatio || 1);
    let scrollY = 0;

    function resize() {
      W = canvas.width  = Math.floor(innerWidth  * pixelRatio);
      H = canvas.height = Math.floor(innerHeight * pixelRatio);
      canvas.style.width  = innerWidth + 'px';
      canvas.style.height = innerHeight + 'px';

      // Initialize multi-layer stars
      const starCount = Math.floor((innerWidth * innerHeight) / 2000);
      stars = new Array(starCount).fill(0).map(() => ({
        x: Math.random() * W,
        y: Math.random() * H,
        z: Math.random() * 0.8 + 0.2, // depth layer
        r: Math.random() * 1.5 + 0.3,
        vx: (Math.random() - .5) * .08,
        vy: (Math.random() - .5) * .08,
        twinkle: Math.random() * Math.PI * 2,
        twinkleSpeed: Math.random() * 0.02 + 0.01,
        color: Math.random() > 0.8 ? '#ffd56b' : (Math.random() > 0.9 ? '#6c63ff' : '#ffffff')
      }));

      // Initialize nebulae
      nebulae = new Array(3).fill(0).map(() => ({
        x: Math.random() * W,
        y: Math.random() * H,
        size: Math.random() * 300 + 200,
        opacity: Math.random() * 0.15 + 0.05,
        color: Math.random() > 0.5 ? 'rgba(108,99,255,' : 'rgba(255,107,53,',
        drift: (Math.random() - 0.5) * 0.02
      }));
    }
    resize();
    addEventListener('resize', resize);

    function createShootingStar() {
      if (Math.random() < 0.002 && shootingStars.length < 3) {
        shootingStars.push({
          x: Math.random() * W,
          y: -50,
          vx: (Math.random() - 0.5) * 4,
          vy: Math.random() * 3 + 2,
          life: 1,
          decay: Math.random() * 0.02 + 0.01,
          trail: []
        });
      }
    }

    function drawNebulae() {
      nebulae.forEach(nebula => {
        nebula.x += nebula.drift;
        if (nebula.x > W + nebula.size) nebula.x = -nebula.size;
        if (nebula.x < -nebula.size) nebula.x = W + nebula.size;

        const grad = ctx.createRadialGradient(
          nebula.x, nebula.y, 0,
          nebula.x, nebula.y, nebula.size
        );
        grad.addColorStop(0, nebula.color + nebula.opacity + ')');
        grad.addColorStop(0.5, nebula.color + (nebula.opacity * 0.5) + ')');
        grad.addColorStop(1, 'transparent');

        ctx.fillStyle = grad;
        ctx.beginPath();
        ctx.arc(nebula.x, nebula.y, nebula.size, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    function drawStars() {
      for (const s of stars) {
        // Parallax movement based on scroll
        s.x += s.vx + (scrollY * s.z * 0.0001);
        s.y += s.vy;
        s.twinkle += s.twinkleSpeed;

        // Wrap around screen
        if (s.x < 0) s.x = W; if (s.x > W) s.x = 0;
        if (s.y < 0) s.y = H; if (s.y > H) s.y = 0;

        // Twinkling effect
        const twinkleAlpha = (Math.sin(s.twinkle) + 1) * 0.3 + 0.4;
        ctx.globalAlpha = s.z * twinkleAlpha;

        // Star glow for brighter stars
        if (s.z > 0.7) {
          ctx.shadowBlur = 4 * pixelRatio;
          ctx.shadowColor = s.color;
        } else {
          ctx.shadowBlur = 0;
        }

        ctx.beginPath();
        ctx.arc(s.x, s.y, s.r * pixelRatio * s.z, 0, Math.PI * 2);
        ctx.fillStyle = s.color;
        ctx.fill();
      }
      ctx.shadowBlur = 0;
    }

    function drawShootingStars() {
      for (let i = shootingStars.length - 1; i >= 0; i--) {
        const star = shootingStars[i];
        star.x += star.vx;
        star.y += star.vy;
        star.life -= star.decay;

        // Add to trail
        star.trail.push({ x: star.x, y: star.y, life: star.life });
        if (star.trail.length > 15) star.trail.shift();

        // Remove if dead or off screen
        if (star.life <= 0 || star.y > H + 100) {
          shootingStars.splice(i, 1);
          continue;
        }

        // Draw trail
        for (let j = 0; j < star.trail.length; j++) {
          const point = star.trail[j];
          const alpha = (j / star.trail.length) * point.life * 0.8;
          ctx.globalAlpha = alpha;
          ctx.fillStyle = '#ffd56b';
          ctx.beginPath();
          ctx.arc(point.x, point.y, (j / star.trail.length) * 3 * pixelRatio, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }

    // Enhanced comet with tail
    let cometT = 0;
    function drawComet() {
      cometT += 0.001;
      const t = (Math.sin(cometT) + 1) / 2;
      const x = t * W * 1.2 - W * 0.1;
      const y = (1 - t) * H * 0.8 + H * 0.1;

      // Comet tail
      for (let i = 0; i < 8; i++) {
        const tailX = x - i * 15;
        const tailY = y + i * 8;
        const alpha = (8 - i) / 8 * 0.3;
        const size = (8 - i) * 20;

        const grad = ctx.createRadialGradient(tailX, tailY, 0, tailX, tailY, size);
        grad.addColorStop(0, `rgba(255,213,107,${alpha})`);
        grad.addColorStop(1, 'transparent');
        ctx.fillStyle = grad;
        ctx.beginPath();
        ctx.arc(tailX, tailY, size, 0, Math.PI * 2);
        ctx.fill();
      }

      // Comet head
      const headGrad = ctx.createRadialGradient(x, y, 0, x, y, 60 * pixelRatio);
      headGrad.addColorStop(0, 'rgba(255,213,107,0.6)');
      headGrad.addColorStop(0.5, 'rgba(255,107,53,0.4)');
      headGrad.addColorStop(1, 'transparent');
      ctx.fillStyle = headGrad;
      ctx.beginPath();
      ctx.arc(x, y, 60 * pixelRatio, 0, Math.PI * 2);
      ctx.fill();
    }

    function loop() {
      ctx.clearRect(0, 0, W, H);

      // Draw in layers for depth
      drawNebulae();
      drawStars();
      createShootingStar();
      drawShootingStars();
      drawComet();

      requestAnimationFrame(loop);
    }
    loop();

    // Update scroll position for parallax
    window.addEventListener('scroll', () => {
      scrollY = window.pageYOffset;
    });

    // =========================
    // 2) ENHANCED LENIS (Smooth Scroll + Advanced Parallax)
    // =========================
    // Lenis is already initialized above, no need to reinitialize

    function raf(time) {
      if (lenis && lenis.raf) {
        lenis.raf(time);
      }
      requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);

    // Enhanced anchor links with smooth transitions
    document.querySelectorAll('a[href^="#"]').forEach(a => a.addEventListener('click', e => {
      const href = a.getAttribute('href');
      if (href.length > 1) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) {
          // Add visual feedback
          if (gsap && gsap.to) {
            gsap.to(a, { scale: 0.95, duration: 0.1, yoyo: true, repeat: 1 });
          }
          if (lenis && lenis.scrollTo) {
            lenis.scrollTo(target, { offset: -80, duration: 1.5, easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)) });
          } else {
            // Fallback smooth scroll
            target.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }
      }
    }));

    // Advanced parallax system
    class AdvancedParallax {
      constructor() {
        this.elements = [];
        this.init();
        this.bindEvents();
      }

      init() {
        // Register parallax elements
        document.querySelectorAll('[data-parallax]').forEach(el => {
          this.elements.push({
            element: el,
            speed: parseFloat(el.getAttribute('data-speed')) || 0.5,
            direction: el.getAttribute('data-direction') || 'vertical',
            offset: 0
          });
        });

        // Add section-based parallax
        document.querySelectorAll('section').forEach((section, index) => {
          this.elements.push({
            element: section,
            speed: 0.1 + (index * 0.05),
            direction: 'vertical',
            offset: 0,
            isSection: true
          });
        });
      }

      update(scroll) {
        this.elements.forEach(item => {
          const { element, speed, direction, isSection } = item;
          const rect = element.getBoundingClientRect();
          const elementTop = rect.top + scroll;
          const elementHeight = rect.height;
          const windowHeight = window.innerHeight;

          // Calculate if element is in viewport
          const isInView = rect.top < windowHeight && rect.bottom > 0;

          if (isInView || isSection) {
            let transform = '';

            if (direction === 'vertical') {
              const yPos = (scroll - elementTop) * speed;
              transform = `translate3d(0, ${yPos}px, 0)`;
            } else if (direction === 'horizontal') {
              const xPos = (scroll - elementTop) * speed;
              transform = `translate3d(${xPos}px, 0, 0)`;
            }

            // Apply transform with hardware acceleration
            element.style.transform = transform;
            element.style.willChange = 'transform';
          }
        });
      }

      bindEvents() {
        if (lenis && lenis.on) {
          lenis.on('scroll', ({ scroll }) => {
            this.update(scroll);
          });
        } else {
          // Fallback to regular scroll events
          window.addEventListener('scroll', () => {
            this.update(window.pageYOffset);
          }, { passive: true });
        }
      }
    }

    const parallaxSystem = new AdvancedParallax();

    // =========================
    // 3) ENHANCED PHOENIX (Anime.js + GSAP + Particles)
    // =========================

    // Phoenix particle system
    class PhoenixParticleSystem {
      constructor() {
        this.canvas = document.getElementById('phoenixParticleCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.phoenixBounds = null;
        this.resize();
        this.init();
      }

      resize() {
        const phoenixEl = document.getElementById('phoenix');
        const rect = phoenixEl.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        this.phoenixBounds = rect;
      }

      init() {
        // Create initial particles
        for (let i = 0; i < 15; i++) {
          this.addParticle();
        }
        this.animate();
      }

      addParticle() {
        this.particles.push({
          x: Math.random() * this.canvas.width,
          y: this.canvas.height * 0.7 + Math.random() * this.canvas.height * 0.3,
          vx: (Math.random() - 0.5) * 0.5,
          vy: -Math.random() * 2 - 1,
          life: 1,
          decay: Math.random() * 0.02 + 0.01,
          size: Math.random() * 3 + 1,
          color: Math.random() > 0.5 ? '#ffd56b' : '#ff6b35'
        });
      }

      animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Update and draw particles
        for (let i = this.particles.length - 1; i >= 0; i--) {
          const p = this.particles[i];
          p.x += p.vx;
          p.y += p.vy;
          p.life -= p.decay;

          if (p.life <= 0) {
            this.particles.splice(i, 1);
            continue;
          }

          // Draw particle
          this.ctx.save();
          this.ctx.globalAlpha = p.life * 0.6;
          this.ctx.fillStyle = p.color;
          this.ctx.shadowBlur = 8;
          this.ctx.shadowColor = p.color;
          this.ctx.beginPath();
          this.ctx.arc(p.x, p.y, p.size * p.life, 0, Math.PI * 2);
          this.ctx.fill();
          this.ctx.restore();
        }

        // Add new particles occasionally
        if (Math.random() < 0.1 && this.particles.length < 20) {
          this.addParticle();
        }

        requestAnimationFrame(() => this.animate());
      }
    }

    // Initialize particle system
    const phoenixParticles = new PhoenixParticleSystem();
    window.addEventListener('resize', () => phoenixParticles.resize());

    // Enhanced phoenix path animations
    const phoenixBody = document.getElementById('phoenixBody');
    const phoenixWings = document.getElementById('phoenixWings');
    const phoenixTail = document.getElementById('phoenixTail');

    // Stroke draw-in animations with stagger
    if (anime) {
      try {
        [phoenixBody, phoenixWings, phoenixTail].forEach((path, i) => {
          if (path && path.getTotalLength) {
            const pathLen = path.getTotalLength();
            path.style.strokeDasharray = pathLen;
            path.style.strokeDashoffset = pathLen;

            anime({
              targets: path,
              strokeDashoffset: [pathLen, 0],
              easing: 'easeInOutSine',
              duration: 2000,
              delay: i * 400
            });
          }
        });
      } catch (error) {
        console.warn('Phoenix path animations failed:', error);
      }
    }

    // Advanced phoenix animations
    if (gsap && gsap.timeline) {
      try {
        gsap.timeline({ repeat: -1 })
          .to('#phoenix', { scale: 1.05, duration: 3, ease: 'sine.inOut' })
          .to('#phoenix', { scale: 1, duration: 3, ease: 'sine.inOut' });

        // Wing flap animation
        gsap.to('#phoenixWings', {
          strokeWidth: [2, 3, 2],
          opacity: [1, 0.8, 1],
          duration: 2.5,
          repeat: -1,
          ease: 'sine.inOut'
        });

        // Flame detail animations
        gsap.to('#flameDetail1, #flameDetail2', {
          strokeWidth: [1.5, 2.5, 1.5],
          opacity: [0.6, 0.9, 0.6],
          duration: 1.8,
          repeat: -1,
          stagger: 0.3,
          ease: 'sine.inOut'
        });
      } catch (error) {
        console.warn('Phoenix GSAP animations failed:', error);
      }
    }

    // Interactive hover effects
    const phoenixSvg = document.getElementById('phoenix');
    phoenixSvg.addEventListener('mouseenter', () => {
      gsap.to('#phoenix', { scale: 1.1, duration: 0.5, ease: 'back.out(1.7)' });
      gsap.to('#phoenixParticles', { opacity: 1, duration: 0.3 });
      // Increase particle generation
      for (let i = 0; i < 5; i++) {
        setTimeout(() => phoenixParticles.addParticle(), i * 100);
      }
    });

    phoenixSvg.addEventListener('mouseleave', () => {
      gsap.to('#phoenix', { scale: 1.05, duration: 0.5, ease: 'power2.out' });
      gsap.to('#phoenixParticles', { opacity: 0.7, duration: 0.3 });
    });

    // =========================
    // 4) PARALLAX (mousemove)
    // =========================
    const parallaxEls = document.querySelectorAll('[data-parallax]');
    addEventListener('mousemove', (e) => {
      const cx = innerWidth / 2, cy = innerHeight / 2;
      const dx = (e.clientX - cx) / cx;
      const dy = (e.clientY - cy) / cy;
      parallaxEls.forEach(el => {
        const speed = parseFloat(el.getAttribute('data-speed')) || 0.04;
        el.style.transform = `translate(${dx * -20 * speed}px, ${dy * -20 * speed}px)`;
      });
    });

    // =========================
    // 5) SCROLL ENTRANCES (GSAP)
    // =========================
    if (gsap && gsap.utils && ScrollTrigger) {
      try {
        gsap.utils.toArray('section').forEach((sec, i) => {
          gsap.from(sec.querySelectorAll('h2, p, .panel, .card, form, svg, .badge'), {
            scrollTrigger: { trigger: sec, start: 'top 70%' },
            y: 24, opacity: 0, stagger: { each: 0.06, from: 'edges' }, duration: .8, ease: 'power3.out'
          });
        });

        // Project card shine rotation
        gsap.utils.toArray('.card .shine').forEach(sh => {
          gsap.to(sh, { rotate: 360, duration: 10, repeat: -1, ease: 'none' });
        });
      } catch (error) {
        console.warn('GSAP scroll animations failed:', error);
      }
    }

    // =========================
    // 6) TERMINAL TYPE (GSAP/Anime)
    // =========================
    const term = document.getElementById('terminalText');
    const lines = [
      '$ docker run -p 8080:8080 api-gateway',
      'Launching PhoenixServer v1.0.0...🔥',
      'Connected to Postgres @ 3ms',
      'Kafka broker sync ✓',
      'OpenTelemetry exporter ready',
      'listening on http://0.0.0.0:8080',
    ];
    let out = '';
    (async function typeLines(){
      for (const line of lines) {
        for (let i = 0; i < line.length; i++) {
          out += line[i];
          term.textContent = out + '█';
          await new Promise(r => setTimeout(r, 12 + Math.random()*30));
        }
        out += '\n'; term.textContent = out;
        await new Promise(r => setTimeout(r, 250));
      }
    })();

    // =========================
    // 7) ENHANCED PROJECT INTERACTIONS & MODALS
    // =========================

    // 3D card tilt effect
    const projectCards = document.querySelectorAll('[data-project]');
    projectCards.forEach(card => {
      card.addEventListener('mousemove', (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;

        gsap.to(card, {
          duration: 0.3,
          rotateX: rotateX,
          rotateY: rotateY,
          transformPerspective: 1000,
          ease: 'power2.out'
        });
      });

      card.addEventListener('mouseleave', () => {
        gsap.to(card, {
          duration: 0.5,
          rotateX: 0,
          rotateY: 0,
          ease: 'power2.out'
        });
      });

      // Enhanced click handler
      card.querySelector('[data-open]')?.addEventListener('click', (e) => {
        e.preventDefault();
        openEnhancedModal(card);
      });
    });

    // Project data for detailed modals
    const projectData = {
      stellar: {
        title: "Stellar API Gateway",
        icon: "🚀",
        description: "Enterprise-grade API gateway designed for extreme performance and reliability.",
        features: [
          "120,000+ requests per second throughput",
          "Sub-millisecond response times",
          "Advanced rate limiting with Redis",
          "JWT authentication & authorization",
          "Comprehensive telemetry & monitoring",
          "Auto-scaling on Kubernetes"
        ],
        tech: ["Node.js", "Express", "Redis", "Kubernetes", "Prometheus", "Grafana"],
        architecture: "Microservices architecture with horizontal scaling, Redis cluster for session management, and Prometheus metrics collection.",
        challenges: "Optimizing for ultra-low latency while maintaining high throughput and implementing circuit breakers for fault tolerance."
      },
      nova: {
        title: "Event Nova",
        icon: "⚡",
        description: "Distributed event streaming platform with guaranteed delivery and exactly-once processing.",
        features: [
          "99.999% uptime SLA",
          "Transactional outbox pattern",
          "Idempotency guarantees",
          "Automatic failover & recovery",
          "Schema evolution support",
          "Dead letter queue handling"
        ],
        tech: ["Kafka", "PostgreSQL", "Docker", "TypeScript", "Zookeeper", "Avro"],
        architecture: "Event-driven architecture with Kafka as the backbone, PostgreSQL for transactional outbox, and custom consumer groups for parallel processing.",
        challenges: "Ensuring exactly-once delivery semantics and handling schema evolution without breaking existing consumers."
      },
      constellation: {
        title: "Constellation Observability",
        icon: "🔭",
        description: "Full-stack observability platform with intelligent alerting and SLO monitoring.",
        features: [
          "Distributed tracing with Jaeger",
          "Custom metrics & dashboards",
          "SLO-based alerting",
          "Anomaly detection",
          "Performance profiling",
          "Cost optimization insights"
        ],
        tech: ["OpenTelemetry", "Grafana", "Jaeger", "Prometheus", "Python", "InfluxDB"],
        architecture: "OpenTelemetry collectors feeding into Jaeger for traces, Prometheus for metrics, with custom Python services for analysis and alerting.",
        challenges: "Correlating traces across microservices and implementing intelligent alerting to reduce noise while catching real issues."
      }
    };

    function openEnhancedModal(card) {
      const projectId = card.getAttribute('data-project');
      const project = projectData[projectId];

      if (!project) return;

      const modal = document.createElement('div');
      modal.innerHTML = `
        <div class="modal-backdrop" style="position:fixed;inset:0;background:rgba(0,0,0,.8);backdrop-filter:blur(12px);display:grid;place-items:center;z-index:100;padding:1rem;">
          <div class="modal" style="max-width:900px;width:90vw;max-height:90vh;background:linear-gradient(135deg, var(--panel), rgba(12,15,22,0.95));border:1px solid rgba(255,213,107,.2);border-radius:24px;overflow:hidden;box-shadow:0 25px 80px rgba(0,0,0,.6);">

            <!-- Modal Header -->
            <div style="padding:2rem 2rem 1rem;background:linear-gradient(135deg, rgba(255,213,107,.1), rgba(255,107,53,.05));border-bottom:1px solid rgba(255,255,255,.1);">
              <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;">
                <div style="display:flex;align-items:center;gap:1rem;">
                  <div style="font-size:2.5rem;">${project.icon}</div>
                  <h2 style="margin:0;font-size:1.8rem;background:linear-gradient(135deg, var(--glow), var(--flame));-webkit-background-clip:text;-webkit-text-fill-color:transparent;">${project.title}</h2>
                </div>
                <button class="btn" id="closeModal" style="background:rgba(255,255,255,.1);border:1px solid rgba(255,255,255,.2);">✕</button>
              </div>
              <p style="color:var(--muted);font-size:1.1rem;margin:0;">${project.description}</p>
            </div>

            <!-- Modal Content -->
            <div style="padding:2rem;overflow-y:auto;max-height:60vh;">
              <div style="display:grid;grid-template-columns:1fr 1fr;gap:2rem;margin-bottom:2rem;">

                <!-- Features -->
                <div>
                  <h3 style="color:var(--glow);margin-bottom:1rem;font-size:1.2rem;">Key Features</h3>
                  <ul style="list-style:none;padding:0;margin:0;">
                    ${project.features.map(feature => `
                      <li style="padding:0.5rem 0;border-bottom:1px solid rgba(255,255,255,.05);color:var(--text);display:flex;align-items:center;gap:0.5rem;">
                        <span style="color:var(--glow);">✦</span> ${feature}
                      </li>
                    `).join('')}
                  </ul>
                </div>

                <!-- Tech Stack -->
                <div>
                  <h3 style="color:var(--glow);margin-bottom:1rem;font-size:1.2rem;">Technology Stack</h3>
                  <div style="display:flex;flex-wrap:wrap;gap:0.5rem;">
                    ${project.tech.map(tech => `
                      <span style="background:rgba(255,213,107,.1);border:1px solid rgba(255,213,107,.3);padding:0.4rem 0.8rem;border-radius:16px;font-size:0.9rem;color:var(--glow);">${tech}</span>
                    `).join('')}
                  </div>
                </div>
              </div>

              <!-- Architecture -->
              <div style="margin-bottom:2rem;">
                <h3 style="color:var(--glow);margin-bottom:1rem;font-size:1.2rem;">Architecture</h3>
                <p style="color:var(--muted);line-height:1.6;">${project.architecture}</p>
              </div>

              <!-- Challenges -->
              <div>
                <h3 style="color:var(--glow);margin-bottom:1rem;font-size:1.2rem;">Technical Challenges</h3>
                <p style="color:var(--muted);line-height:1.6;">${project.challenges}</p>
              </div>
            </div>
          </div>
        </div>`;

      document.body.appendChild(modal);
      document.body.style.overflow = 'hidden';

      const modalBox = modal.querySelector('.modal');
      const backdrop = modal.querySelector('.modal-backdrop');

      // Entrance animation
      gsap.set(modalBox, { scale: 0.8, opacity: 0, y: 50 });
      gsap.set(backdrop, { opacity: 0 });

      gsap.timeline()
        .to(backdrop, { opacity: 1, duration: 0.3 })
        .to(modalBox, {
          scale: 1,
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: 'back.out(1.7)'
        }, '-=0.1');

      // Close handlers
      const closeModal = () => {
        gsap.timeline()
          .to(modalBox, {
            scale: 0.9,
            opacity: 0,
            y: 30,
            duration: 0.4,
            ease: 'power2.in'
          })
          .to(backdrop, {
            opacity: 0,
            duration: 0.3
          }, '-=0.2')
          .call(() => {
            modal.remove();
            document.body.style.overflow = '';
          });
      };

      modal.addEventListener('click', (e) => {
        if (e.target === backdrop) closeModal();
      });

      modal.querySelector('#closeModal').addEventListener('click', closeModal);

      // Escape key handler
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          closeModal();
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);
    }

    // =========================
    // 8) SKILL CLOUD animation (Anime.js)
    // =========================
    anime({ targets: '#labels text', opacity: [0,1], translateY: [-6,0], delay: anime.stagger(80), easing:'easeOutQuad' });
    anime({ targets: '#orbits circle', strokeDasharray: ['0 1000','8 1000'], delay: anime.stagger(120), duration: 1200, easing:'easeInOutSine' });

    // =========================
    // MOBILE OPTIMIZATIONS
    // =========================

    // Detect mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isMobile || isTouchDevice) {
      // Reduce particle count for mobile
      if (typeof phoenixParticles !== 'undefined') {
        phoenixParticles.particles = phoenixParticles.particles.slice(0, 8);
      }

      // Simplify starfield for mobile
      if (stars.length > 50) {
        stars = stars.slice(0, 50);
      }

      // Disable complex animations on mobile
      document.body.classList.add('mobile-device');

      // Add touch feedback
      document.querySelectorAll('.btn, .card').forEach(el => {
        el.addEventListener('touchstart', () => {
          el.style.transform = 'scale(0.98)';
        });

        el.addEventListener('touchend', () => {
          setTimeout(() => {
            el.style.transform = '';
          }, 150);
        });
      });

      // Optimize scroll performance on mobile
      let ticking = false;
      const updateScrollPosition = () => {
        scrollY = window.pageYOffset;
        ticking = false;
      };

      window.addEventListener('scroll', () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollPosition);
          ticking = true;
        }
      }, { passive: true });
    }

    // Viewport height fix for mobile browsers
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', () => {
      setTimeout(setVH, 100);
    });

    // Battery API optimization (if available)
    if ('getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        if (battery.level < 0.2) {
          // Reduce animations when battery is low
          document.body.classList.add('low-battery');
          console.log('Low battery detected, reducing animations');
        }
      });
    }

    // Network-aware loading
    if ('connection' in navigator) {
      const connection = navigator.connection;
      if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
        // Disable heavy animations on slow connections
        document.body.classList.add('slow-connection');
        console.log('Slow connection detected, optimizing experience');
      }
    }

    // Footer year
    document.getElementById('year').textContent = new Date().getFullYear();

    // Final performance measurement
    window.addEventListener('load', () => {
      performance.mark('portfolio-loaded');
      performance.measure('portfolio-load-time', 'navigationStart', 'portfolio-loaded');
    });

    }; // Close initializeApp function
  </script>

  <!-- Additional mobile-specific styles -->
  <style>
    .mobile-device .card .shine,
    .mobile-device #phoenixParticleCanvas,
    .low-battery .card .shine,
    .low-battery #phoenixParticleCanvas,
    .slow-connection .card .shine,
    .slow-connection #phoenixParticleCanvas {
      display: none !important;
    }

    .mobile-device .card:hover,
    .low-battery .card:hover,
    .slow-connection .card:hover {
      transform: none !important;
    }

    /* Use CSS custom property for viewport height */
    .hero {
      min-height: calc(var(--vh, 1vh) * 100);
    }
  </style>
</body>
</html>
