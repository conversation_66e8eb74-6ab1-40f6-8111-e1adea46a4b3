<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Space Phoenix – Backend Developer Portfolio</title>
  <meta name="description" content="Backend developer portfolio with space theme, phoenix SVG, Lenis smooth scroll, GSAP, and Anime.js animations." />
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://unpkg.com" crossorigin>
  <style>
    :root {
      --bg:#07070a;           /* deep space */
      --panel:#0c0f16;        /* panels */
      --text:#eaeaf2;         /* base text */
      --muted:#9aa0a6;        /* muted text */
      --flame:#ff6b35;        /* phoenix flame */
      --glow:#ffd56b;         /* soft glow */
      --accent:#6c63ff;       /* cosmic purple */
      --grid: 1200px;
    }

    * { box-sizing: border-box; }
    html, body {
      height: 100%;
      background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
                  radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
                  var(--bg);
      color: var(--text);
      margin: 0;
      font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
      overflow-x: hidden;
    }
    a { color: var(--glow); text-decoration: none; }
    a:hover { text-decoration: underline; }

    /* Global layout */
    header {
      position: fixed; inset-inline: 0; top: 0; z-index: 50;
      backdrop-filter: saturate(120%) blur(10px);
      background: linear-gradient(to bottom, rgba(7,7,10,.75), transparent);
      border-bottom: 1px solid rgba(255,255,255,.06);
    }
    .nav {
      max-width: var(--grid); margin: 0 auto; padding: .6rem 1rem; display:flex; align-items:center; justify-content:space-between;
    }
    .brand { display:flex; gap:.6rem; align-items:center; font-weight:700; letter-spacing:.3px; }
    .dot { width: 10px; height: 10px; border-radius: 9999px; background: linear-gradient(180deg, var(--glow), var(--flame)); box-shadow: 0 0 12px var(--glow); }
    .menu { display:flex; gap:1rem; font-size:.95rem; }
    .menu a { opacity:.9 }

    main { position: relative; }

    /* Starfield canvas */
    #starfield { position: fixed; inset:0; z-index:-2; display:block; }
    /* Subtle dust overlay for depth */
    .space-dust { position: fixed; inset:0; pointer-events:none; z-index:-1; background: url('data:image/svg+xml;utf8,\
      <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">\
        <filter id="f"><feTurbulence baseFrequency="0.9" numOctaves="2" type="fractalNoise"/></filter>\
        <rect width="200" height="200" opacity="0.03" filter="url(%23f)"/>\
      </svg>') repeat; mix-blend-mode: screen; }

    /* Sections */
    section { padding: 7rem 1rem; }
    .wrap { max-width: var(--grid); margin: 0 auto; }

    /* HERO */
    .hero { display:grid; min-height: 100dvh; place-items:center; position: relative; }
    .hero-inner { display:grid; grid-template-columns: 1.2fr 1fr; gap: 3rem; align-items:center; width: 100%; }
    .hero p.kicker { color: var(--muted); text-transform: uppercase; font-size: .8rem; letter-spacing: .2em; margin: 0 0 .6rem; }
    .title { font-size: clamp(2.2rem, 5vw, 4rem); line-height: 1.05; margin: 0 0 1rem; font-weight: 800; }
    .subtitle { color: var(--muted); font-size: clamp(1rem, 2vw, 1.2rem); max-width: 60ch; }

    .cta { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
    .btn { border:1px solid rgba(255,255,255,.12); background: rgba(255,255,255,.03); color: var(--text); padding:.8rem 1rem; border-radius: 16px; font-weight:600; cursor:pointer; }
    .btn.primary { background: linear-gradient(180deg, rgba(255,213,107,.22), rgba(255,107,53,.22)); border-color: rgba(255,213,107,.45); box-shadow: 0 6px 30px rgba(255,213,107,.18); }
    .btn:hover { transform: translateY(-2px); }

    .phoenix-wrap { position: relative; }
    .phoenix-glow { position:absolute; inset: -10%; filter: blur(40px); opacity:.35; background: radial-gradient(closest-side, var(--glow), transparent 65%); border-radius: 50%; }
    svg#phoenix { width: min(480px, 38vw); display:block; transform-origin: 50% 50%; }

    /* ABOUT */
    .panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.00)); border:1px solid rgba(255,255,255,.06); border-radius: 20px; padding: 1.2rem; }
    .about { display:grid; gap: 1.2rem; grid-template-columns: 1.2fr .8fr; align-items: stretch; }
    .terminal { background: #0a0d13; border-radius: 14px; border:1px solid rgba(255,255,255,.06); position: relative; overflow:hidden; }
    .terminal .bar { display:flex; gap:.5rem; align-items:center; padding:.6rem .8rem; border-bottom:1px solid rgba(255,255,255,.06); background: linear-gradient(180deg, rgba(255,255,255,.04), transparent); }
    .status-dot { width:10px; height:10px; border-radius:9999px; background:#ff5f56; box-shadow: 0 0 8px rgba(255,95,86,.8); }
    .status-dot.yellow { background:#ffbd2e; box-shadow:0 0 8px rgba(255,189,46,.8); }
    .status-dot.green { background:#27c93f; box-shadow:0 0 8px rgba(39,201,63,.8); }
    .terminal pre { margin: 0; padding: 1rem; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; color: #c8e1ff; font-size: .95rem; min-height: 220px; white-space: pre-wrap; }

    /* PROJECTS */
    .projects-grid { display:grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; }
    .card { position:relative; overflow:hidden; border-radius: 18px; background: var(--panel); border:1px solid rgba(255,255,255,.06); padding:1.1rem; min-height: 230px; }
    .card:hover { transform: translateY(-4px); box-shadow: 0 12px 40px rgba(0,0,0,.35); }
    .card h3 { margin:.2rem 0 .4rem; }
    .card p { color: var(--muted); }
    .card .shine { position:absolute; inset:-20%; background: conic-gradient(from 0deg, rgba(255,255,255,.06), transparent 30%, transparent 70%, rgba(255,255,255,.06)); transform: rotate(0deg); opacity:.4; pointer-events:none; }

    /* SKILLS */
    .skills { display:grid; grid-template-columns: .9fr 1.1fr; gap: 2rem; align-items:center; }
    .skill-cloud { position: relative; aspect-ratio: 1/1; }
    .skill-cloud svg { width: 100%; height: auto; display:block; }
    .badge { display:inline-flex; gap:.5rem; align-items:center; background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.08); padding:.5rem .7rem; border-radius: 9999px; margin: .25rem; font-size: .95rem; }

    /* CONTACT */
    form { display:grid; gap: .9rem; }
    label { font-size:.9rem; color: var(--muted); }
    input, textarea { width:100%; background: rgba(255,255,255,.03); border:1px solid rgba(255,255,255,.1); color: var(--text); border-radius: 12px; padding:.8rem; outline: none; }
    input:focus, textarea:focus { border-color: var(--glow); box-shadow: 0 0 0 3px rgba(255,213,107,.15); }
    textarea { min-height: 140px; }

    footer { padding: 2rem 1rem 3rem; color: var(--muted); text-align:center; }

    /* Utils */
    .muted { color: var(--muted); }
    .grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

    /* Responsive */
    @media (max-width: 1000px) {
      .hero-inner { grid-template-columns: 1fr; text-align:center; }
      .phoenix-wrap { order:-1; }
      .about { grid-template-columns: 1fr; }
      .projects-grid { grid-template-columns: 1fr 1fr; }
      .skills { grid-template-columns: 1fr; }
    }
    @media (max-width: 640px) {
      .projects-grid { grid-template-columns: 1fr; }
    }
  </style>
</head>
<body>
  <canvas id="starfield"></canvas>
  <div class="space-dust" aria-hidden="true"></div>
  <header>
    <nav class="nav">
      <div class="brand"><span class="dot"></span> <span>Space Phoenix</span></div>
      <div class="menu">
        <a href="#hero">Home</a>
        <a href="#about">About</a>
        <a href="#projects">Projects</a>
        <a href="#skills">Tech</a>
        <a href="#contact">Contact</a>
      </div>
    </nav>
  </header>

  <main>
    <!-- HERO -->
    <section id="hero" class="hero">
      <div class="wrap hero-inner">
        <div>
          <p class="kicker">Backend Developer</p>
          <h1 class="title">Rising from Code. Building the Future.</h1>
          <p class="subtitle">I design resilient APIs, distributed systems, and developer tooling. Obsessed with performance, observability, and elegant architectures.</p>
          <div class="cta">
            <a class="btn primary" href="#projects">View Projects</a>
            <a class="btn" href="#contact">Get in Touch</a>
          </div>
        </div>
        <div class="phoenix-wrap" data-parallax data-speed="0.05">
          <div class="phoenix-glow" aria-hidden="true"></div>
          <!-- Minimal stylized Phoenix SVG (two morphable paths) -->
          <svg id="phoenix" viewBox="0 0 512 512" fill="none" stroke="url(#grad)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
            <defs>
              <linearGradient id="grad" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stop-color="var(--glow)"/>
                <stop offset="50%" stop-color="var(--flame)"/>
                <stop offset="100%" stop-color="var(--accent)"/>
              </linearGradient>
            </defs>
            <path id="phoenixPath" d="M256 60c-38 62 6 116-20 168-20 38-72 42-116 26 62 26 112 6 144-36 32 42 82 62 144 36-44 16-96 12-116-26-26-52 18-106-20-168Zm0 0c-6 22-8 44-8 66m8-66c6 22 8 44 8 66M116 248c38 20 72 38 92 70-18 16-44 20-68 14 28 10 52 26 62 52-18 2-36-2-50-10 34 30 66 60 104 78 38-18 70-48 104-78-14 8-32 12-50 10 10-26 34-42 62-52-24 6-50 2-68-14 20-32 54-50 92-70"/>
          </svg>
        </div>
      </div>
    </section>

    <!-- ABOUT -->
    <section id="about">
      <div class="wrap about">
        <div class="panel">
          <h2>About Me</h2>
          <p class="muted">I build back-ends that don’t blink under load. Experience with Node.js, Express, NestJS, Python (FastAPI), PostgreSQL, Redis, Kafka, Docker, and Kubernetes. I care about clean boundaries, excellent DX, and making systems observable.</p>
          <div class="grid-2" style="margin-top: .6rem;">
            <div class="badge">Node.js</div>
            <div class="badge">Express</div>
            <div class="badge">NestJS</div>
            <div class="badge">TypeScript</div>
            <div class="badge">Python</div>
            <div class="badge">FastAPI</div>
            <div class="badge">PostgreSQL</div>
            <div class="badge">Redis</div>
            <div class="badge">Kafka</div>
            <div class="badge">Docker</div>
            <div class="badge">Kubernetes</div>
            <div class="badge">AWS</div>
          </div>
        </div>
        <div class="terminal">
          <div class="bar">
            <span class="status-dot"></span>
            <span class="status-dot yellow"></span>
            <span class="status-dot green"></span>
            <span style="margin-left:.5rem; opacity:.8;">~/phoenix/run.sh</span>
          </div>
          <pre id="terminalText"></pre>
        </div>
      </div>
    </section>

    <!-- PROJECTS -->
    <section id="projects">
      <div class="wrap">
        <h2>Projects</h2>
        <p class="muted">Selected work with a focus on APIs, scalability, and reliability.</p>
        <div class="projects-grid" style="margin-top:1rem;">
          <article class="card" data-project>
            <div class="shine" aria-hidden="true"></div>
            <h3>Stellar API Gateway</h3>
            <p>Rate-limiting, JWT auth, telemetry; 120k rps on k8s. GSAP reveals & hover lift.</p>
            <a class="btn" style="margin-top:.8rem;" data-open>Details</a>
          </article>
          <article class="card" data-project>
            <div class="shine" aria-hidden="true"></div>
            <h3>Event Nova</h3>
            <p>Kafka-based event platform with outbox + idempotency. Five 9s goal.</p>
            <a class="btn" style="margin-top:.8rem;" data-open>Details</a>
          </article>
          <article class="card" data-project>
            <div class="shine" aria-hidden="true"></div>
            <h3>Constellation Observability</h3>
            <p>OpenTelemetry traces + metrics + logs. SLO dashboards & alerts.</p>
            <a class="btn" style="margin-top:.8rem;" data-open>Details</a>
          </article>
        </div>
      </div>
    </section>

    <!-- SKILLS -->
    <section id="skills">
      <div class="wrap skills">
        <div>
          <h2>Tech Stack</h2>
          <p class="muted">A toolkit tuned for building reliable services and great developer experiences.</p>
          <div style="margin-top:.6rem; display:flex; flex-wrap:wrap;">
            <div class="badge">Node.js</div>
            <div class="badge">TypeScript</div>
            <div class="badge">Python</div>
            <div class="badge">FastAPI</div>
            <div class="badge">PostgreSQL</div>
            <div class="badge">Prisma/TypeORM</div>
            <div class="badge">Redis</div>
            <div class="badge">Kafka</div>
            <div class="badge">Docker</div>
            <div class="badge">Kubernetes</div>
            <div class="badge">AWS</div>
            <div class="badge">OpenTelemetry</div>
          </div>
        </div>
        <div class="skill-cloud" aria-hidden="true">
          <!-- Circular orbit labels, animated with Anime.js -->
          <svg viewBox="0 0 400 400">
            <defs>
              <filter id="softGlow">
                <feGaussianBlur stdDeviation="3" result="blur"/>
                <feMerge>
                  <feMergeNode in="blur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <g id="orbits" fill="none" stroke="rgba(255,255,255,.12)">
              <circle cx="200" cy="200" r="60"/>
              <circle cx="200" cy="200" r="110"/>
              <circle cx="200" cy="200" r="160"/>
            </g>
            <g id="labels" font-size="12" text-anchor="middle" fill="currentColor" filter="url(#softGlow)">
              <text x="200" y="35">Node</text>
              <text x="330" y="90">Postgres</text>
              <text x="365" y="210">Redis</text>
              <text x="320" y="320">Kafka</text>
              <text x="200" y="365">Docker</text>
              <text x="80"  y="320">Kubernetes</text>
              <text x="35"  y="210">AWS</text>
              <text x="70"  y="90">OpenTelemetry</text>
            </g>
            <circle id="core" cx="200" cy="200" r="8" fill="url(#grad)"/>
          </svg>
        </div>
      </div>
    </section>

    <!-- CONTACT -->
    <section id="contact">
      <div class="wrap">
        <h2>Contact</h2>
        <p class="muted">Have a challenge that needs firepower? I’m open to collaborations and roles.</p>
        <form>
          <div class="grid-2">
            <div>
              <label for="name">Name</label>
              <input id="name" placeholder="Ada Lovelace" />
            </div>
            <div>
              <label for="email">Email</label>
              <input id="email" type="email" placeholder="<EMAIL>" />
            </div>
          </div>
          <div>
            <label for="msg">Message</label>
            <textarea id="msg" placeholder="Tell me about your project..."></textarea>
          </div>
          <button class="btn primary" type="button">Send</button>
        </form>
      </div>
    </section>

    <footer>
      <div class="wrap">
        <div class="muted">© <span id="year"></span> Space Phoenix — Built with HTML, CSS, and JavaScript. Lenis × GSAP × Anime.js</div>
      </div>
    </footer>
  </main>

  <!-- Vendor libs -->
  <script type="module">
    import Lenis from 'https://unpkg.com/@studio-freight/lenis@1.1.21/dist/lenis.min.js';
    import { gsap } from 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js';
    import { ScrollTrigger } from 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js';
    import anime from 'https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.es.js';

    gsap.registerPlugin(ScrollTrigger);

    // =========================
    // 1) STARFIELD (Canvas)
    // =========================
    const canvas = document.getElementById('starfield');
    const ctx = canvas.getContext('2d');
    let stars = [], W, H, pixelRatio = Math.min(2, window.devicePixelRatio || 1);

    function resize() {
      W = canvas.width  = Math.floor(innerWidth  * pixelRatio);
      H = canvas.height = Math.floor(innerHeight * pixelRatio);
      canvas.style.width  = innerWidth + 'px';
      canvas.style.height = innerHeight + 'px';
      // initialize stars based on area
      const count = Math.floor((innerWidth * innerHeight) / 2500);
      stars = new Array(count).fill(0).map(() => ({
        x: Math.random() * W,
        y: Math.random() * H,
        z: Math.random() * 0.6 + 0.4,
        r: Math.random() * 1.2 + 0.2,
        vx: (Math.random() - .5) * .05,
        vy: (Math.random() - .5) * .05
      }));
    }
    resize();
    addEventListener('resize', resize);

    function drawStars() {
      ctx.clearRect(0, 0, W, H);
      for (const s of stars) {
        s.x += s.vx; s.y += s.vy;
        if (s.x < 0) s.x = W; if (s.x > W) s.x = 0;
        if (s.y < 0) s.y = H; if (s.y > H) s.y = 0;
        ctx.globalAlpha = s.z;
        ctx.beginPath();
        ctx.arc(s.x, s.y, s.r * pixelRatio, 0, Math.PI * 2);
        ctx.fillStyle = '#fff';
        ctx.fill();
      }
    }

    // comet pass
    let cometT = 0;
    function drawComet() {
      cometT += 0.002;
      const t = (Math.sin(cometT) + 1) / 2; // 0..1
      const x = t * W, y = (1 - t) * H * 0.6 + H * 0.2;
      const grad = ctx.createRadialGradient(x, y, 0, x, y, 140 * pixelRatio);
      grad.addColorStop(0, 'rgba(255,213,107,.25)');
      grad.addColorStop(1, 'rgba(255,213,107,0)');
      ctx.fillStyle = grad; ctx.beginPath();
      ctx.arc(x, y, 140 * pixelRatio, 0, Math.PI * 2);
      ctx.fill();
    }

    function loop() {
      drawStars();
      drawComet();
      requestAnimationFrame(loop);
    }
    loop();

    // =========================
    // 2) LENIS (Smooth Scroll)
    // =========================
    const lenis = new Lenis({ lerp: 0.08, wheelMultiplier: 1.1, smoothWheel: true, smoothTouch: false });
    function raf(time) { lenis.raf(time); requestAnimationFrame(raf); }
    requestAnimationFrame(raf);

    // Anchor links smooth via Lenis
    document.querySelectorAll('a[href^="#"]').forEach(a => a.addEventListener('click', e => {
      const href = a.getAttribute('href');
      if (href.length > 1) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) lenis.scrollTo(target, { offset: -60 });
      }
    }));

    // =========================
    // 3) PHOENIX (Anime.js)
    // =========================
    const phoenixPath = document.getElementById('phoenixPath');
    // Stroke draw-in
    const pathLen = phoenixPath.getTotalLength();
    phoenixPath.style.strokeDasharray = pathLen;
    phoenixPath.style.strokeDashoffset = pathLen;

    anime({
      targets: '#phoenixPath',
      strokeDashoffset: [pathLen, 0],
      easing: 'easeInOutSine',
      duration: 2800
    });

    // Gentle glow + floating
    gsap.to('#phoenix', { scale: 1.04, yoyo: true, repeat: -1, duration: 2.8, ease: 'sine.inOut' });

    // =========================
    // 4) PARALLAX (mousemove)
    // =========================
    const parallaxEls = document.querySelectorAll('[data-parallax]');
    addEventListener('mousemove', (e) => {
      const cx = innerWidth / 2, cy = innerHeight / 2;
      const dx = (e.clientX - cx) / cx;
      const dy = (e.clientY - cy) / cy;
      parallaxEls.forEach(el => {
        const speed = parseFloat(el.getAttribute('data-speed')) || 0.04;
        el.style.transform = `translate(${dx * -20 * speed}px, ${dy * -20 * speed}px)`;
      });
    });

    // =========================
    // 5) SCROLL ENTRANCES (GSAP)
    // =========================
    gsap.utils.toArray('section').forEach((sec, i) => {
      gsap.from(sec.querySelectorAll('h2, p, .panel, .card, form, svg, .badge'), {
        scrollTrigger: { trigger: sec, start: 'top 70%' },
        y: 24, opacity: 0, stagger: { each: 0.06, from: 'edges' }, duration: .8, ease: 'power3.out'
      });
    });

    // Project card shine rotation
    gsap.utils.toArray('.card .shine').forEach(sh => {
      gsap.to(sh, { rotate: 360, duration: 10, repeat: -1, ease: 'none' });
    });

    // =========================
    // 6) TERMINAL TYPE (GSAP/Anime)
    // =========================
    const term = document.getElementById('terminalText');
    const lines = [
      '$ docker run -p 8080:8080 api-gateway',
      'Launching PhoenixServer v1.0.0...🔥',
      'Connected to Postgres @ 3ms',
      'Kafka broker sync ✓',
      'OpenTelemetry exporter ready',
      'listening on http://0.0.0.0:8080',
    ];
    let out = '';
    (async function typeLines(){
      for (const line of lines) {
        for (let i = 0; i < line.length; i++) {
          out += line[i];
          term.textContent = out + '█';
          await new Promise(r => setTimeout(r, 12 + Math.random()*30));
        }
        out += '\n'; term.textContent = out;
        await new Promise(r => setTimeout(r, 250));
      }
    })();

    // =========================
    // 7) MODALS (Anime.js) – demo only
    // =========================
    const projects = document.querySelectorAll('[data-project]');
    projects.forEach((card) => {
      card.querySelector('[data-open]')?.addEventListener('click', () => openModal(card));
    });

    function openModal(card) {
      const modal = document.createElement('div');
      modal.innerHTML = `
        <div class="modal-backdrop" style="position:fixed;inset:0;background:rgba(0,0,0,.6);backdrop-filter:blur(6px);display:grid;place-items:center;z-index:60;">
          <div class="modal" style="max-width:720px;width:clamp(300px,80vw,720px);background:var(--panel);border:1px solid rgba(255,255,255,.08);border-radius:16px;overflow:hidden;">
            <div style="padding:1rem 1rem .2rem; display:flex; justify-content:space-between; align-items:center;">
              <h3 style="margin:0">${card.querySelector('h3').textContent}</h3>
              <button class="btn" id="closeModal">Close</button>
            </div>
            <div style="padding: 0 1rem 1rem; color: var(--muted);">
              <p>Deep-dive: architecture diagram, request flow, scaling notes, trade-offs, and results. Replace this with your content or link to a case study page.</p>
            </div>
          </div>
        </div>`;
      document.body.appendChild(modal);
      const box = modal.querySelector('.modal');
      anime({ targets: box, translateY: [40,0], opacity:[0,1], easing:'spring(1, 80, 10, 0)', duration: 600 });
      modal.addEventListener('click', (e)=>{ if(e.target===modal.firstElementChild) close(); });
      modal.querySelector('#closeModal').addEventListener('click', close);
      function close(){ anime({ targets: box, translateY:[0,20], opacity:[1,0], duration:300, easing:'easeInOutSine', complete(){ modal.remove(); } }); }
    }

    // =========================
    // 8) SKILL CLOUD animation (Anime.js)
    // =========================
    anime({ targets: '#labels text', opacity: [0,1], translateY: [-6,0], delay: anime.stagger(80), easing:'easeOutQuad' });
    anime({ targets: '#orbits circle', strokeDasharray: ['0 1000','8 1000'], delay: anime.stagger(120), duration: 1200, easing:'easeInOutSine' });

    // Footer year
    document.getElementById('year').textContent = new Date().getFullYear();
  </script>
</body>
</html>
